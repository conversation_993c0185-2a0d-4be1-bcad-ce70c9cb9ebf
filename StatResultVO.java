package com.example.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.example.converter.MoneyConverter;
import lombok.Data;
import java.util.Date;

/**
 * 统计查询结果VO
 */
@Data
public class StatResultVO {

    // fd子查询字段
    @ExcelProperty(value = "收款时间", index = 0)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date receptTime;

    @ExcelProperty(value = "账户名称", index = 1)
    private String accountName;

    @ExcelProperty(value = "费用名称", index = 2)
    private String feeName;

    @ExcelProperty(value = "费用金额(元)", index = 3, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long feeAmount;

    @ExcelProperty(value = "订单ID", index = 4)
    @NumberFormat("#")
    private Long orderId;

    @ExcelProperty(value = "收款机构名称", index = 5)
    private String receptOrgName;

    // vehicle_order表字段
    @ExcelProperty(value = "订单编号", index = 6)
    private String orderSn;

    @ExcelProperty(value = "订单日期", index = 7)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date orderDate;

    @ExcelProperty(value = "销售机构ID", index = 8)
    @NumberFormat("#")
    private Long salesOrgId;

    @ExcelProperty(value = "销售顾问ID", index = 9)
    @NumberFormat("#")
    private Long salesAgentId;

    @ExcelProperty(value = "启票总价(元)", index = 10, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long sbAmount;

    @ExcelProperty(value = "优惠金额(元)", index = 11, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long discountAmount;

    @ExcelProperty(value = "优惠金额转车款", index = 12)
    private Boolean discountDeductible;

    @ExcelProperty(value = "销售费用(元)", index = 13, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long salesCostAmount;

    @ExcelProperty(value = "成交总价(元)", index = 14, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long dealAmount;

    @ExcelProperty(value = "付款方式", index = 15)
    private String paymentMethod;

    @ExcelProperty(value = "贷款渠道", index = 16)
    private String loanChannel;

    @ExcelProperty(value = "贷款期数", index = 17)
    private Integer loanMonths;

    @ExcelProperty(value = "贷款金额(元)", index = 18, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long loanAmount;

    @ExcelProperty(value = "首付金额(元)", index = 19, converter = MoneyConverter.class)
    @NumberFormat("#0.00")
    @ColumnWidth(15)
    private long loanInitialAmount;

    @ExcelProperty(value = "首付比例", index = 20)
    @NumberFormat("0.00%")
    private Double loanInitialRatio;

    @ExcelProperty(value = "返利转车款", index = 21)
    private Boolean loanRebatePayableDeductible;

    @ExcelProperty(value = "二手车车牌号", index = 22)
    private String usedVehicleId;

    @ExcelProperty(value = "二手车VIN", index = 23)
    private String usedVehicleVin;

    @ExcelProperty(value = "置换金额(分)", index = 24)
    @NumberFormat("#")
    private long usedVehicleAmount;

    @ExcelProperty(value = "置换补贴(分)", index = 25)
    @NumberFormat("#")
    private long usedVehicleDeductibleAmount;

    @ExcelProperty(value = "收厂家补贴金额(分)", index = 26)
    @NumberFormat("#")
    private long usedVehicleDiscountReceivableAmount;

    @ExcelProperty(value = "付客户补贴金额(分)", index = 27)
    @NumberFormat("#")
    private long usedVehicleDiscountPayableAmount;

    @ExcelProperty(value = "补贴转车款", index = 28)
    private Boolean usedVehicleDiscountPayableDeductible;

    @ExcelProperty(value = "二手车品牌", index = 29)
    private String usedVehicleBrand;

    @ExcelProperty(value = "二手车车型", index = 30)
    private String usedVehicleModel;

    @ExcelProperty(value = "二手车颜色", index = 31)
    private String usedVehicleColor;

    @ExcelProperty(value = "定金转车款", index = 32)
    private Boolean depositDeductible;

    @ExcelProperty(value = "交车装备(分)", index = 33)
    @NumberFormat("#")
    private long deliveryEquipment;

    @ExcelProperty(value = "是否有赠品", index = 34)
    private Flag hasGiftItems;

    @ExcelProperty(value = "是否有保险", index = 35)
    private Flag hasInsurance;

    @ExcelProperty(value = "分期服务费(分)", index = 36)
    @NumberFormat("#")
    private long loanFee;

    @ExcelProperty(value = "应收分期返利(分)", index = 37)
    @NumberFormat("#")
    private long loanRebateReceivableAmount;

    @ExcelProperty(value = "应付分期返利(分)", index = 38)
    @NumberFormat("#")
    private long loanRebatePayableAmount;

    @ExcelProperty(value = "专项优惠金额(分)", index = 39)
    @NumberFormat("#")
    private long exclusiveDiscountPayableAmount;

    @ExcelProperty(value = "专项优惠应收金额(分)", index = 40)
    @NumberFormat("#")
    private long exclusiveDiscountReceivableAmount;

    @ExcelProperty(value = "专项优惠类型", index = 41)
    private String exclusiveDiscountType;

    @ExcelProperty(value = "专项优惠抵车款", index = 42)
    private Boolean exclusiveDiscountPayableDeductible;

    @ExcelProperty(value = "专项优惠说明", index = 43)
    private String exclusiveDiscountRemark;

    @ExcelProperty(value = "销售金额(分)", index = 44)
    @NumberFormat("#")
    private long salesAmount;

    @ExcelProperty(value = "发票金额(分)", index = 45)
    @NumberFormat("#")
    private long invoiceAmount;

    @ExcelProperty(value = "赠品项目", index = 46)
    private String giftItems;

    @ExcelProperty(value = "销售地类型", index = 47)
    private String salesStoreType;

    @ExcelProperty(value = "是否存在专项优惠", index = 48)
    private Flag hasExclusiveDiscount;

    @ExcelProperty(value = "二网备注", index = 49)
    private String salesStoreRemark;

    @ExcelProperty(value = "直营店名称", index = 50)
    private String salesStoreName;

    // stock_outbound_bill表字段
    @ExcelProperty(value = "车架号VIN", index = 51)
    private String vin;

    @ExcelProperty(value = "出库机构ID", index = 52)
    @NumberFormat("#")
    private Long outboundOrgId;

    @ExcelProperty(value = "出库人ID", index = 53)
    @NumberFormat("#")
    private Long outboundAgentId;

    @ExcelProperty(value = "出库时间", index = 54)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date outboundTime;

    // vehicle_stock表字段
    @ExcelProperty(value = "库存金额(分)", index = 55)
    @NumberFormat("#")
    private long stockAmount;

    @ExcelProperty(value = "库龄天数", index = 56)
    private int stockDays;

    @ExcelProperty(value = "启票机构名称", index = 57)
    private String sbOrgName;

    @ExcelProperty(value = "仓储机构名称", index = 58)
    private String stockOrgName;

    @ExcelProperty(value = "试乘试驾状态", index = 59)
    private String trialStatus;

    @ExcelProperty(value = "库存类型", index = 60)
    private String stockType;

    @ExcelProperty(value = "折旧金额(分)", index = 61)
    @NumberFormat("#")
    private long depreciationAmount;

    @ExcelProperty(value = "厂家支持金额(分)", index = 62)
    @NumberFormat("#")
    private long manufacturerSupportAmount;

    // derivative_costs表字段
    @ExcelProperty(value = "公证费收入(分)", index = 63)
    @NumberFormat("#")
    private long notaryFeeIncome;

    @ExcelProperty(value = "公证费成本(分)", index = 64)
    @NumberFormat("#")
    private long notaryFeeCost;

    @ExcelProperty(value = "畅行无忧收入(分)", index = 65)
    @NumberFormat("#")
    private long dcCarefreeIncome;

    @ExcelProperty(value = "畅行无忧成本(分)", index = 66)
    @NumberFormat("#")
    private long carefreeCost;

    @ExcelProperty(value = "延保收入(分)", index = 67)
    @NumberFormat("#")
    private long dcExtendedWarrantyIncome;

    @ExcelProperty(value = "延保成本(分)", index = 68)
    @NumberFormat("#")
    private long extendedWarrantyCost;

    @ExcelProperty(value = "VPS收入(分)", index = 69)
    @NumberFormat("#")
    private long dcVpsIncome;

    @ExcelProperty(value = "VPS成本(分)", index = 70)
    @NumberFormat("#")
    private long vpsCost;

    @ExcelProperty(value = "预收利息收入(分)", index = 71)
    @NumberFormat("#")
    private long preInterestIncome;

    @ExcelProperty(value = "预收利息成本(分)", index = 72)
    @NumberFormat("#")
    private long preInterestCost;

    @ExcelProperty(value = "牌照费收入(分)", index = 73)
    @NumberFormat("#")
    private long licensePlateFeeIncome;

    @ExcelProperty(value = "牌照费成本(分)", index = 74)
    @NumberFormat("#")
    private long licensePlateFeeCost;

    @ExcelProperty(value = "临牌费收入(分)", index = 75)
    @NumberFormat("#")
    private long tempPlateFeeIncome;

    @ExcelProperty(value = "临牌费成本(分)", index = 76)
    @NumberFormat("#")
    private long tempPlateFeeCost;

    @ExcelProperty(value = "交车装备收入(分)", index = 77)
    @NumberFormat("#")
    private long deliveryEquipmentIncome;

    @ExcelProperty(value = "交车装备成本(分)", index = 78)
    @NumberFormat("#")
    private long deliveryEquipmentCost;

    @ExcelProperty(value = "其他收入(分)", index = 79)
    @NumberFormat("#")
    private long dcOtherIncome;

    @ExcelProperty(value = "其他成本(分)", index = 80)
    @NumberFormat("#")
    private long otherCost;

    // vehicle_sku表字段
    @ExcelProperty(value = "品牌", index = 81)
    private String brand;

    @ExcelProperty(value = "车系", index = 82)
    private String series;

    @ExcelProperty(value = "车型代码", index = 83)
    private String modelCode;

    @ExcelProperty(value = "车型名称", index = 84)
    private String modelName;

    @ExcelProperty(value = "配置代码", index = 85)
    private String configCode;

    @ExcelProperty(value = "配置名称", index = 86)
    private String configName;

    @ExcelProperty(value = "颜色代码", index = 87)
    private String colorCode;

    @ExcelProperty(value = "启票单价(分)", index = 88)
    @NumberFormat("#")
    private long sbPrice;

    // vehicle_insurance表字段
    @ExcelProperty(value = "保险公司", index = 89)
    private String insuranceProvider;

    @ExcelProperty(value = "交强险保费(分)", index = 90)
    @NumberFormat("#")
    private long ciFee;

    @ExcelProperty(value = "交强险返利(分)", index = 91)
    @NumberFormat("#")
    private long ciRebate;

    @ExcelProperty(value = "商业险保费(分)", index = 92)
    @NumberFormat("#")
    private long biFee;

    @ExcelProperty(value = "商业险返利(分)", index = 93)
    @NumberFormat("#")
    private long biRebate;

    @ExcelProperty(value = "非车险保费(分)", index = 94)
    @NumberFormat("#")
    private long oiFee;

    @ExcelProperty(value = "非车险返利(分)", index = 95)
    @NumberFormat("#")
    private long oiRebate;

    @ExcelProperty(value = "返利状态", index = 96)
    private String rebateStatus;

    // crm_customer表字段
    @ExcelProperty(value = "客户分类", index = 97)
    private String customerType;

    @ExcelProperty(value = "客户归属机构名称", index = 98)
    private String cusOwnerOrgName;

    @ExcelProperty(value = "客户归属销售顾问", index = 99)
    private String ownerSellerName;

    @ExcelProperty(value = "客户名称", index = 100)
    private String customerName;

    @ExcelProperty(value = "联系方式", index = 101)
    private String mobile;
}
