package com.example.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import lombok.Data;
import java.util.Date;

/**
 * 统计查询结果VO
 */
@Data
public class StatResultVO {

    // fd子查询字段
    private Date receptTime;
    private String accountName;
    private String feeName;
    private long feeAmount;
    private Long orderId;
    private String receptOrgName;

    // vehicle_order表字段
    private String orderSn;
    private Date orderDate;
    private Long salesOrgId;
    private Long salesAgentId;
    private long sbAmount;
    private long discountAmount;
    private Boolean discountDeductible;
    private long salesCostAmount;
    private long dealAmount;
    private String paymentMethod;
    private String loanChannel;
    private Integer loanMonths;
    private long loanAmount;
    private long loanInitialAmount;
    private Double loanInitialRatio;
    private Boolean loanRebatePayableDeductible;
    private String usedVehicleId;
    private String usedVehicleVin;
    private long usedVehicleAmount;
    private long usedVehicleDeductibleAmount;
    private long usedVehicleDiscountReceivableAmount;
    private long usedVehicleDiscountPayableAmount;
    private Boolean usedVehicleDiscountPayableDeductible;
    private String usedVehicleBrand;
    private String usedVehicleModel;
    private String usedVehicleColor;
    private Boolean depositDeductible;
    private long deliveryEquipment;
    private Flag hasGiftItems;
    private Flag hasInsurance;
    private long loanFee;
    private long loanRebateReceivableAmount;
    private long loanRebatePayableAmount;
    private long exclusiveDiscountPayableAmount;
    private long exclusiveDiscountReceivableAmount;
    private String exclusiveDiscountType;
    private Boolean exclusiveDiscountPayableDeductible;
    private String exclusiveDiscountRemark;
    private long salesAmount;
    private long invoiceAmount;
    private String giftItems;
    private String salesStoreType;
    private Flag hasExclusiveDiscount;
    private String salesStoreRemark;
    private String salesStoreName;

    // stock_outbound_bill表字段
    private String vin;
    private Long outboundOrgId;
    private Long outboundAgentId;
    private Date outboundTime;

    // vehicle_stock表字段
    private long stockAmount;
    private int stockDays;
    private String sbOrgName;
    private String stockOrgName;
    private String trialStatus;
    private String stockType;
    private long depreciationAmount;
    private long manufacturerSupportAmount;

    // derivative_costs表字段
    private long notaryFeeIncome;
    private long notaryFeeCost;
    private long dcCarefreeIncome;
    private long carefreeCost;
    private long dcExtendedWarrantyIncome;
    private long extendedWarrantyCost;
    private long dcVpsIncome;
    private long vpsCost;
    private long preInterestIncome;
    private long preInterestCost;
    private long licensePlateFeeIncome;
    private long licensePlateFeeCost;
    private long tempPlateFeeIncome;
    private long tempPlateFeeCost;
    private long deliveryEquipmentIncome;
    private long deliveryEquipmentCost;
    private long dcOtherIncome;
    private long otherCost;

    // vehicle_sku表字段
    private String brand;
    private String series;
    private String modelCode;
    private String modelName;
    private String configCode;
    private String configName;
    private String colorCode;
    private long sbPrice;

    // vehicle_insurance表字段
    private String insuranceProvider;
    private long ciFee;
    private long ciRebate;
    private long biFee;
    private long biRebate;
    private long oiFee;
    private long oiRebate;
    private String rebateStatus;

    // crm_customer表字段
    private String customerType;
    private String cusOwnerOrgName;
    private String ownerSellerName;
    private String customerName;
    private String mobile;
}
