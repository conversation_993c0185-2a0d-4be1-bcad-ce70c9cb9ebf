select
	-- fd子查询字段
	fd.receptTime,
	fd.accountName,
	fd.feeName,
	fd.feeAmount,
	fd.orderId,
	fd.receptOrgName,

	-- vehicle_order表字段
	vo.order_type as orderType,
	vo.order_sn as orderSn,
	vo.order_date as orderDate,
	vo.customer_id as customerId,
	vo.sales_org_id as salesOrgId,
	vo.sales_agent_id as salesAgentId,
	vo.sales_leader_id as salesLeaderId,
	vo.order_status as orderStatus,
	vo.sku_id as skuId,
	vo.delivery_date as deliveryDate,
	vo.delivery_org_id as deliveryOrgId,
	vo.sb_amount as sbAmount,
	vo.discount_amount as discountAmount,
	vo.discount_deductible as discountDeductible,
	vo.sales_cost_amount as salesCostAmount,
	vo.deal_amount as dealAmount,
	vo.deal_amount_cn as dealAmountCn,
	vo.payment_method as paymentMethod,
	vo.loan_channel as loanChannel,
	vo.loan_months as loanMonths,
	vo.loan_amount as loanAmount,
	vo.loan_initial_amount as loanInitialAmount,
	vo.loan_initial_ratio as loanInitialRatio,
	vo.loan_rebate_payable_deductible as loanRebatePayableDeductible,
	vo.used_vehicle_id as usedVehicleId,
	vo.used_vehicle_vin as usedVehicleVin,
	vo.used_vehicle_amount as usedVehicleAmount,
	vo.used_vehicle_deductible_amount as usedVehicleDeductibleAmount,
	vo.used_vehicle_discount_receivable_amount as usedVehicleDiscountReceivableAmount,
	vo.used_vehicle_discount_payable_amount as usedVehicleDiscountPayableAmount,
	vo.used_vehicle_discount_payable_deductible as usedVehicleDiscountPayableDeductible,
	vo.used_vehicle_brand as usedVehicleBrand,
	vo.used_vehicle_model as usedVehicleModel,
	vo.used_vehicle_color as usedVehicleColor,
	vo.deposit_type as depositType,
	vo.deposit_amount as depositAmount,
	vo.deposit_deductible as depositDeductible,
	vo.deposit_usable as depositUsable,
	vo.carefree_income as carefreeIncome,
	vo.delivery_equipment as deliveryEquipment,
	vo.extended_warranty_income as extendedWarrantyIncome,
	vo.has_derivative_income as hasDerivativeIncome,
	vo.has_gift_items as hasGiftItems,
	vo.has_insurance as hasInsurance,
	vo.license_plate_fee as licensePlateFee,
	vo.notary_fee as notaryFee,
	vo.pre_interest as preInterest,
	vo.temp_plate_fee as tempPlateFee,
	vo.vps_income as vpsIncome,
	vo.other_income as otherIncome,
	vo.loan_fee as loanFee,
	vo.loan_rebate_receivable_amount as loanRebateReceivableAmount,
	vo.loan_rebate_payable_amount as loanRebatePayableAmount,
	vo.remark as remark,
	vo.exclusive_discount_payable_amount as exclusiveDiscountPayableAmount,
	vo.exclusive_discount_receivable_amount as exclusiveDiscountReceivableAmount,
	vo.exclusive_discount_type as exclusiveDiscountType,
	vo.exclusive_discount_payable_deductible as exclusiveDiscountPayableDeductible,
	vo.exclusive_discount_remark as exclusiveDiscountRemark,
	vo.sales_amount as salesAmount,
	vo.invoice_amount as invoiceAmount,
	vo.gift_items as giftItems,
	vo.insurance_org_id as insuranceOrgId,
	vo.sales_store_type as salesStoreType,
	vo.has_exclusive_discount as hasExclusiveDiscount,
	vo.recept_status as receptStatus,
	vo.sales_store_remark as salesStoreRemark,
	vo.sales_store_name as salesStoreName,

	-- stock_outbound_bill表字段
	ob.order_id as obOrderId,
	ob.outbound_org_id as outboundOrgId,
	ob.outbound_agent_id as outboundAgentId,
	ob.outbound_time as outboundTime,
	ob.sku_id as obSkuId,
	ob.vin as obVin,
	ob.outbound_status as outboundStatus,

	-- vehicle_stock表字段
	vs.sku_id as vsSkuId,
	vs.vin as vsVin,
	vs.stock_amount as stockAmount,
	vs.stock_status as stockStatus,
	vs.stock_days as stockDays,
	vs.sb_org_id as sbOrgId,
	vs.sb_org_name as sbOrgName,
	vs.owner_org_id as ownerOrgId,
	vs.owner_org_name as ownerOrgName,
	vs.stock_org_id as stockOrgId,
	vs.stock_org_name as stockOrgName,
	vs.trialing_begin_date as trialingBeginDate,
	vs.trial_status as trialStatus,
	vs.stock_type as stockType,
	vs.transfer_status as transferStatus,
	vs.depreciation_amount as depreciationAmount,
	vs.manufacturer_support_amount as manufacturerSupportAmount,

	-- derivative_costs表字段
	dc.order_sn as dcOrderSn,
	dc.vin as dcVin,
	dc.confirmed as confirmed,
	dc.editor_org_id as dcEditorOrgId,
	dc.editor_org_name as dcEditorOrgName,
	dc.editor_id as dcEditorId,
	dc.editor_name as dcEditorName,
	dc.notary_fee_income as notaryFeeIncome,
	dc.notary_fee_cost as notaryFeeCost,
	dc.carefree_income as dcCarefreeIncome,
	dc.carefree_cost as carefreeCost,
	dc.extended_warranty_income as dcExtendedWarrantyIncome,
	dc.extended_warranty_cost as extendedWarrantyCost,
	dc.vps_income as dcVpsIncome,
	dc.vps_cost as vpsCost,
	dc.pre_interest_income as preInterestIncome,
	dc.pre_interest_cost as preInterestCost,
	dc.license_plate_fee_income as licensePlateFeeIncome,
	dc.license_plate_fee_cost as licensePlateFeeCost,
	dc.temp_plate_fee_income as tempPlateFeeIncome,
	dc.temp_plate_fee_cost as tempPlateFeeCost,
	dc.delivery_equipment_income as deliveryEquipmentIncome,
	dc.delivery_equipment_cost as deliveryEquipmentCost,
	dc.other_income as dcOtherIncome,
	dc.other_cost as otherCost,

	-- vehicle_sku表字段
	sku.brand as brand,
	sku.series as series,
	sku.model_code as modelCode,
	sku.model_name as modelName,
	sku.config_code as configCode,
	sku.config_name as configName,
	sku.sku_id as skuSkuId,
	sku.color_code as colorCode,
	sku.sb_price as sbPrice,

	-- vehicle_insurance表字段
	vi.vin as viVin,
	vi.insurance_provider as insuranceProvider,
	vi.ci_fee as ciFee,
	vi.ci_rebate as ciRebate,
	vi.bi_fee as biFee,
	vi.bi_rebate as biRebate,
	vi.oi_fee as oiFee,
	vi.oi_rebate as oiRebate,
	vi.rebate_status as rebateStatus,
	vi.biz_org_id as viBizOrgId,
	vi.biz_agent_id as bizAgentId

from
(
select
	recept_time as receptTime,
	fas.abbr as accountName,
	rit.fee_name as feeName,
	rit.fee_amount as feeAmount,
	source_id as orderId,
	org.`org_name` as receptOrgName
FROM financial_recept_item rit
INNER JOIN financial_recept_bill rpb on rit.recept_id = rpb.id
INNER JOIN financial_receivable_bill rvb on rit.receivable_id = rvb.id
INNER JOIN financial_accounts_setting fas on account_id = fas.id
INNER JOIN biz_org org on fas.owner_org_id = org.id
WHERE recept_time between '2025-07-01' and '2025-07-30'
AND rit.fee_id in (1001,1008)
AND source_table = 'vehicle_order'
) as fd
INNER JOIN vehicle_order vo on fd.orderId = vo.id
INNER JOIN stock_outbound_bill ob on vo.id = ob.order_id
INNER JOIN vehicle_stock vs on ob.vin = vs.vin
INNER JOIN derivative_costs dc on ob.vin = dc.vin
INNER JOIN vehicle_sku sku on ob.sku_id = sku.id
INNER J
LEFT JOIN vehicle_insurance vi on ob.vin = vi.vin

