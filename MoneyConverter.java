package com.example.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金额转换器：将分转换为元显示
 * 用于EasyExcel导出时将long类型的分值转换为以元为单位的小数
 */
public class MoneyConverter implements Converter<Long> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Long.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.NUMBER;
    }

    /**
     * 写入Excel时的转换：将分转换为元
     */
    @Override
    public WriteCellData<?> convertToExcelData(Long value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return new WriteCellData<>(BigDecimal.ZERO);
        }
        
        // 将分转换为元，保留2位小数
        BigDecimal yuan = BigDecimal.valueOf(value)
                .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        
        return new WriteCellData<>(yuan);
    }

    /**
     * 从Excel读取时的转换：将元转换为分（如果需要读取功能）
     */
    @Override
    public Long convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (cellData.getNumberValue() == null) {
            return 0L;
        }
        
        // 将元转换为分
        return cellData.getNumberValue().multiply(BigDecimal.valueOf(100)).longValue();
    }
}
