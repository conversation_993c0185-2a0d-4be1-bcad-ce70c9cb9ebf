// 修正后的上传方法，直接替换你现有的uploadData方法

public String uploadData(String fileName, List<StatOutboundVO> data) {
    String fileKey = "exports/" + System.nanoTime() + "_" + fileName + ".xlsx";
    try {
        // 1. 直接写入到ByteArrayOutputStream，不使用GZIP压缩
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        // 2. 使用EasyExcel写入Excel数据
        EasyExcel.write(outputStream, StatOutboundVO.class)
                .sheet("统计数据")
                .needHead(true) // 确保使用注解中的表头名称
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动调整列宽
                .doWrite(data);
        
        // 3. 获取Excel文件字节数组
        byte[] excelContent = outputStream.toByteArray();
        outputStream.close();
        
        // 4. 创建输入流
        InputStream inputStream = new ByteArrayInputStream(excelContent);

        // 5. 设置对象元数据
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        metadata.setContentLength(excelContent.length); // 重要：设置内容长度
        
        // 6. 设置文件下载时的文件名（修正文件名编码）
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
        metadata.setContentDisposition("attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");
        
        // 7. 上传到COS
        cosClient.putObject(bucketName, fileKey, inputStream, metadata);
        inputStream.close();
        
        log.info("Excel文件上传COS成功，文件key：{}, 数据条数：{}", fileKey, data.size());
        
        // 8. 返回CDN访问地址（修正URL拼接）
        return cdnHost + "/" + fileKey;
        
    } catch (Exception e) {
        log.error("Excel文件上传COS失败，文件名：{}", fileName, e);
        throw new RuntimeException("Excel文件上传COS失败: " + e.getMessage());
    }
}

// 主要修正点：
// 1. 移除了GZIP压缩 - Excel文件本身已经是压缩格式，再次压缩会导致文件损坏
// 2. 添加了metadata.setContentLength(excelContent.length) - 设置正确的内容长度
// 3. 修正了文件名编码问题
// 4. 修正了CDN URL拼接（添加了"/"分隔符）
// 5. 改进了异常处理和日志记录
// 6. 确保所有流都正确关闭
