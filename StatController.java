package com.example.controller;

import com.example.service.StatService;
import com.example.util.StatExcelExporter;
import com.example.vo.StatResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 统计数据控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/stat")
@RequiredArgsConstructor
public class StatController {

    private final StatService statService;
    private final StatExcelExporter statExcelExporter;

    /**
     * 导出统计数据到Excel
     * 
     * @param response HTTP响应对象
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    @GetMapping("/export")
    public void exportStatData(HttpServletResponse response,
                              @RequestParam String startDate,
                              @RequestParam String endDate) {
        try {
            log.info("开始导出统计数据，时间范围：{} - {}", startDate, endDate);
            
            // 查询统计数据
            List<StatResultVO> dataList = statService.getStatData(startDate, endDate);
            
            if (dataList.isEmpty()) {
                log.warn("查询结果为空，时间范围：{} - {}", startDate, endDate);
                // 可以选择返回空文件或者抛出异常
            }
            
            // 生成文件名
            String fileName = String.format("统计数据_%s_%s", startDate.replace("-", ""), endDate.replace("-", ""));
            
            // 导出Excel
            statExcelExporter.exportToExcel(response, dataList, fileName);
            
            log.info("统计数据导出完成，数据条数：{}", dataList.size());
            
        } catch (Exception e) {
            log.error("统计数据导出失败", e);
            throw new RuntimeException("统计数据导出失败: " + e.getMessage());
        }
    }

    /**
     * 查询统计数据（不导出，用于预览）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    @GetMapping("/query")
    public List<StatResultVO> queryStatData(@RequestParam String startDate,
                                           @RequestParam String endDate) {
        log.info("查询统计数据，时间范围：{} - {}", startDate, endDate);
        return statService.getStatData(startDate, endDate);
    }

    /**
     * 导出统计数据到服务器本地文件（用于测试）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param filePath 文件路径
     * @return 导出结果信息
     */
    @PostMapping("/export-file")
    public String exportToFile(@RequestParam String startDate,
                              @RequestParam String endDate,
                              @RequestParam String filePath) {
        try {
            log.info("开始导出统计数据到本地文件，时间范围：{} - {}，文件路径：{}", startDate, endDate, filePath);
            
            List<StatResultVO> dataList = statService.getStatData(startDate, endDate);
            statExcelExporter.exportToFile(filePath, dataList);
            
            return String.format("导出成功，数据条数：%d，文件路径：%s", dataList.size(), filePath);
            
        } catch (Exception e) {
            log.error("导出到本地文件失败", e);
            return "导出失败: " + e.getMessage();
        }
    }
}
