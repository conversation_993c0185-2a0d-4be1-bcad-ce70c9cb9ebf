// 需要添加MoneyConverter的金额字段列表和对应的修改

// 1. salesCostAmount
// 原：@ExcelProperty(value = "销售费用(分)", index = 13)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "销售费用(元)", index = 13, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 2. dealAmount
// 原：@ExcelProperty(value = "成交总价(分)", index = 14)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "成交总价(元)", index = 14, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 3. loanAmount
// 原：@ExcelProperty(value = "贷款金额(分)", index = 18)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "贷款金额(元)", index = 18, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 4. loanInitialAmount
// 原：@ExcelProperty(value = "首付金额(分)", index = 19)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "首付金额(元)", index = 19, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 5. usedVehicleAmount
// 原：@ExcelProperty(value = "置换金额(分)", index = 24)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "置换金额(元)", index = 24, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 6. usedVehicleDeductibleAmount
// 原：@ExcelProperty(value = "置换补贴(分)", index = 25)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "置换补贴(元)", index = 25, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 7. usedVehicleDiscountReceivableAmount
// 原：@ExcelProperty(value = "收厂家补贴金额(分)", index = 26)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "收厂家补贴金额(元)", index = 26, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 8. usedVehicleDiscountPayableAmount
// 原：@ExcelProperty(value = "付客户补贴金额(分)", index = 27)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "付客户补贴金额(元)", index = 27, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 9. deliveryEquipment
// 原：@ExcelProperty(value = "交车装备(分)", index = 33)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "交车装备(元)", index = 33, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 10. loanFee
// 原：@ExcelProperty(value = "分期服务费(分)", index = 36)
//     @NumberFormat("#")
// 改：@ExcelProperty(value = "分期服务费(元)", index = 36, converter = MoneyConverter.class)
//     @NumberFormat("#0.00")
//     @ColumnWidth(15)

// 继续其他金额字段...
// 由于字段太多，建议分批次修改
