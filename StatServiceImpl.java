package com.example.service.impl;

import com.example.mapper.StatMapper;
import com.example.service.StatService;
import com.example.vo.StatResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 统计数据服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatServiceImpl implements StatService {

    private final StatMapper statMapper;

    @Override
    public List<StatResultVO> getStatData(String startDate, String endDate) {
        log.info("查询统计数据，时间范围：{} - {}", startDate, endDate);
        
        try {
            List<StatResultVO> result = statMapper.getStatData(startDate, endDate);
            log.info("统计数据查询完成，数据条数：{}", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("统计数据查询失败，时间范围：{} - {}", startDate, endDate, e);
            throw new RuntimeException("统计数据查询失败", e);
        }
    }
}
