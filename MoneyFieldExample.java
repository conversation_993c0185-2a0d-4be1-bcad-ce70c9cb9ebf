// 金额字段使用转换器的完整示例

// 方案1：使用自定义转换器（推荐）
@ExcelProperty(value = "费用金额(元)", index = 3, converter = MoneyConverter.class)
@NumberFormat("#0.00")
@ColumnWidth(15)
private long feeAmount;

// 方案2：在POJO中添加计算属性（简单但会增加字段）
@ExcelProperty(value = "费用金额(元)", index = 3)
@NumberFormat("#0.00")
@ColumnWidth(15)
public BigDecimal getFeeAmountYuan() {
    return BigDecimal.valueOf(feeAmount).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
}

// 方案3：使用EasyExcel的内置转换器配置
@ExcelProperty(value = "费用金额(元)", index = 3)
@NumberFormat("#0.00")
@ColumnWidth(15)
private BigDecimal feeAmountYuan; // 在Service层转换后赋值

// 推荐使用方案1，因为：
// 1. 保持原有字段不变
// 2. 转换逻辑集中在转换器中
// 3. 可复用于所有金额字段
// 4. 不增加额外的字段或方法

// 如果你想快速应用到所有金额字段，可以批量替换：
// 将所有包含"(分)"的@ExcelProperty注解中的：
// - "(分)" 替换为 "(元)"
// - 添加 ", converter = MoneyConverter.class"
// - "@NumberFormat("#")" 替换为 "@NumberFormat("#0.00")"
// - 添加 "@ColumnWidth(15)"

// 需要修改的金额字段列表：
/*
feeAmount - 费用金额
sbAmount - 启票总价
discountAmount - 优惠金额
salesCostAmount - 销售费用
dealAmount - 成交总价
loanAmount - 贷款金额
loanInitialAmount - 首付金额
usedVehicleAmount - 置换金额
usedVehicleDeductibleAmount - 置换补贴
usedVehicleDiscountReceivableAmount - 收厂家补贴金额
usedVehicleDiscountPayableAmount - 付客户补贴金额
deliveryEquipment - 交车装备
loanFee - 分期服务费
loanRebateReceivableAmount - 应收分期返利
loanRebatePayableAmount - 应付分期返利
exclusiveDiscountPayableAmount - 专项优惠金额
exclusiveDiscountReceivableAmount - 专项优惠应收金额
salesAmount - 销售金额
invoiceAmount - 发票金额
stockAmount - 库存金额
depreciationAmount - 折旧金额
manufacturerSupportAmount - 厂家支持金额
notaryFeeIncome - 公证费收入
notaryFeeCost - 公证费成本
dcCarefreeIncome - 畅行无忧收入
carefreeCost - 畅行无忧成本
dcExtendedWarrantyIncome - 延保收入
extendedWarrantyCost - 延保成本
dcVpsIncome - VPS收入
vpsCost - VPS成本
preInterestIncome - 预收利息收入
preInterestCost - 预收利息成本
licensePlateFeeIncome - 牌照费收入
licensePlateFeeCost - 牌照费成本
tempPlateFeeIncome - 临牌费收入
tempPlateFeeCost - 临牌费成本
deliveryEquipmentIncome - 交车装备收入
deliveryEquipmentCost - 交车装备成本
dcOtherIncome - 其他收入
otherCost - 其他成本
sbPrice - 启票单价
ciFee - 交强险保费
ciRebate - 交强险返利
biFee - 商业险保费
biRebate - 商业险返利
oiFee - 非车险保费
oiRebate - 非车险返利
*/
