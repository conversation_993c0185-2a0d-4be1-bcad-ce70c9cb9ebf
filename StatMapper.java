package com.example.mapper;

import com.example.vo.StatResultVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 统计数据Mapper接口
 */
@Mapper
public interface StatMapper {
    
    /**
     * 获取统计数据
     * 
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return 统计数据列表
     */
    List<StatResultVO> getStatData(@Param("startDate") String startDate, 
                                  @Param("endDate") String endDate);
}
