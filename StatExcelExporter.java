package com.example.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.example.vo.StatResultVO;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 统计数据Excel导出工具类
 */
@Slf4j
@Component
public class StatExcelExporter {

    private final COSClient cosClient;

    @Value("${cos.bucket-name}")
    private String bucketName;

    @Value("${cos.cdn-host}")
    private String cdnHost;

    public StatExcelExporter(COSClient cosClient) {
        this.cosClient = cosClient;
    }

    /**
     * 导出统计数据到Excel文件
     * 
     * @param response HTTP响应对象
     * @param dataList 统计数据列表
     * @param fileName 文件名（不包含扩展名）
     */
    public void exportToExcel(HttpServletResponse response, List<StatResultVO> dataList, String fileName) {
        try {
            // 设置响应头
            setResponseHeaders(response, fileName);
            
            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream(), StatResultVO.class)
                    .sheet("统计数据")
                    .needHead(true) // 确保使用注解中的表头名称
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动调整列宽
                    .doWrite(dataList);
                    
            log.info("Excel导出成功，文件名：{}, 数据条数：{}", fileName, dataList.size());
            
        } catch (IOException e) {
            log.error("Excel导出失败", e);
            throw new RuntimeException("Excel导出失败", e);
        }
    }

    /**
     * 导出统计数据到Excel文件（使用默认文件名）
     * 
     * @param response HTTP响应对象
     * @param dataList 统计数据列表
     */
    public void exportToExcel(HttpServletResponse response, List<StatResultVO> dataList) {
        String defaultFileName = "统计数据_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        exportToExcel(response, dataList, defaultFileName);
    }

    /**
     * 导出统计数据到本地文件
     * 
     * @param filePath 文件路径（包含文件名和扩展名）
     * @param dataList 统计数据列表
     */
    public void exportToFile(String filePath, List<StatResultVO> dataList) {
        try {
            EasyExcel.write(filePath, StatResultVO.class)
                    .sheet("统计数据")
                    .needHead(true) // 确保使用注解中的表头名称
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动调整列宽
                    .doWrite(dataList);
                    
            log.info("Excel文件导出成功，文件路径：{}, 数据条数：{}", filePath, dataList.size());
            
        } catch (Exception e) {
            log.error("Excel文件导出失败，文件路径：{}", filePath, e);
            throw new RuntimeException("Excel文件导出失败", e);
        }
    }

    /**
     * 上传Excel数据到COS
     *
     * @param fileName 文件名（不包含扩展名）
     * @param dataList 统计数据列表
     * @return COS文件访问URL
     */
    public String uploadToCOS(String fileName, List<StatResultVO> dataList) {
        String fileKey = "exports/" + System.nanoTime() + "_" + fileName + ".xlsx";
        try {
            // 直接写入到ByteArrayOutputStream，不使用GZIP压缩
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            EasyExcel.write(outputStream, StatResultVO.class)
                    .sheet("统计数据")
                    .needHead(true) // 确保使用注解中的表头名称
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动调整列宽
                    .doWrite(dataList);

            // 获取Excel文件字节数组
            byte[] excelContent = outputStream.toByteArray();
            outputStream.close();

            // 创建输入流
            InputStream inputStream = new ByteArrayInputStream(excelContent);

            // 设置对象元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            metadata.setContentLength(excelContent.length); // 设置内容长度

            // 设置文件下载时的文件名
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
            metadata.setContentDisposition("attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");

            // 上传到COS
            cosClient.putObject(bucketName, fileKey, inputStream, metadata);
            inputStream.close();

            log.info("Excel文件上传COS成功，文件key：{}, 数据条数：{}", fileKey, dataList.size());

            // 返回CDN访问地址
            return cdnHost + "/" + fileKey;

        } catch (Exception e) {
            log.error("Excel文件上传COS失败，文件名：{}", fileName, e);
            throw new RuntimeException("Excel文件上传COS失败", e);
        }
    }

    /**
     * 设置HTTP响应头
     *
     * @param response HTTP响应对象
     * @param fileName 文件名
     * @throws IOException IO异常
     */
    private void setResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        // 防止中文乱码
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
    }
}
