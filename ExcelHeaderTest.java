package com.example.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.example.vo.StatResultVO;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel表头测试类
 */
public class ExcelHeaderTest {
    
    public static void main(String[] args) {
        // 创建测试数据
        List<StatResultVO> testData = new ArrayList<>();
        StatResultVO vo = new StatResultVO();
        vo.setAccountName("测试账户");
        vo.setFeeName("测试费用");
        vo.setFeeAmount(100000L);
        testData.add(vo);
        
        // 测试Excel导出
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        EasyExcel.write(outputStream, StatResultVO.class)
                .sheet("统计数据")
                .needHead(true) // 确保使用注解中的表头名称
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(testData);
        
        byte[] excelContent = outputStream.toByteArray();
        System.out.println("Excel文件大小: " + excelContent.length + " bytes");
        
        // 如果你想保存到本地文件进行测试
        // EasyExcel.write("test.xlsx", StatResultVO.class)
        //         .sheet("统计数据")
        //         .needHead(true)
        //         .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
        //         .doWrite(testData);
    }
}

// 如果仍然显示字段名而不是中文名称，请检查以下几点：

// 1. 确保EasyExcel版本正确（推荐3.x版本）
// <dependency>
//     <groupId>com.alibaba</groupId>
//     <artifactId>easyexcel</artifactId>
//     <version>3.3.2</version>
// </dependency>

// 2. 确保注解导入正确
// import com.alibaba.excel.annotation.ExcelProperty;

// 3. 如果还是不行，可以尝试不使用index，只使用value：
// @ExcelProperty("收款时间")
// private Date receptTime;

// 4. 或者尝试使用order属性：
// @ExcelProperty(value = "收款时间", order = 0)
// private Date receptTime;
